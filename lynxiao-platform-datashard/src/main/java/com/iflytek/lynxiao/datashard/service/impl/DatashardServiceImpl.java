package com.iflytek.lynxiao.datashard.service.impl;

import com.iflytek.lynxiao.common.datashard.DatashardDocApi.*;
import com.iflytek.lynxiao.datashard.domain.DataDocument;
import com.iflytek.lynxiao.datashard.exception.ErrorCode;
import com.iflytek.lynxiao.datashard.service.DatashardService;
import com.iflytek.lynxiao.datashard.util.CompressionUtil;
import com.iflytek.lynxiao.datashard.util.HashUtil;
import com.mongodb.bulk.BulkWriteResult;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.BulkOperationException;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.Assert;
import skynet.boot.common.utils.MD5Util;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Implementation of data shard service for document storage, query and deletion.
 * <p>
 * Key features:
 * - Document compression for storage efficiency
 * - MD5-based collection routing
 * - Asynchronous hit count tracking
 * - Reference-based document management
 * <p>
 * Monitoring Metrics:
 * 1. lynxiao.datashard.increase
 * - Tracks document storage operations
 * - Tags:
 * - type=data: New document creation
 * - type=ref: Reference addition to existing document
 * <p>
 * 2. lynxiao.datashard.hit
 * - Tracks document access frequency
 * - Tags:
 * - exist=true: Successful document retrieval
 * - exist=false: Document not found or invalid request
 * <p>
 * 3. lynxiao.datashard.remove
 * - Tracks document deletion operations
 * - Tags:
 * - type=data: Complete document deletion
 * - type=ref: Reference removal from document
 * <p>
 * 4. lynxiao.datashard.text.length
 * - Tracks text document length statistics
 * - Uses Summary metric type to track distribution
 */
@Slf4j
public class DatashardServiceImpl implements DatashardService {

    private final MongoTemplate mongoTemplate;
    private final MeterRegistry meterRegistry;
    private final Executor asyncExecutor;

    // Metric name constants
    private static final String METRIC_STORE = "lynxiao.datashard.increase";
    private static final String METRIC_HIT = "lynxiao.datashard.hit";
    private static final String METRIC_REMOVE = "lynxiao.datashard.remove";
    private static final String METRIC_TEXT_LENGTH = "lynxiao.datashard.text.length";
    private static final String METRIC_MONGODB_OPERATION = "lynxiao.datashard.mongodb.operation";

    private static final Update UPDATE = new Update().inc("hit", 1);

    private final Executor executor;

    /**
     * Initialize datashard service with MongoDB template and metrics.
     *
     * @param mongoTemplate  MongoDB template
     * @param meterRegistry  Metrics registry
     * @param threadPoolSize Thread pool size for async operations
     */
    public DatashardServiceImpl(MongoTemplate mongoTemplate, MeterRegistry meterRegistry, int threadPoolSize) {
        this.mongoTemplate = mongoTemplate;
        this.meterRegistry = meterRegistry;
        this.asyncExecutor = Executors.newFixedThreadPool(threadPoolSize);
        log.info("Initialized async thread pool with size: {}", threadPoolSize);

        executor = Executors.newFixedThreadPool(threadPoolSize);
    }

    /**
     * Store text documents in MongoDB.
     * Adds business reference if document exists, otherwise creates a new document.
     * Optimized to batch operations by collection name to reduce MongoDB requests.
     *
     * @param items List of text document items to store
     * @return List of storage results
     */
    @Override
    public List<TextStoreResponseItem> storeTextDocuments(List<TextStoreRequestItem> items) {
        Assert.notEmpty(items, "Document items cannot be empty");
        items.forEach(item -> {
            Assert.hasText(item.getRefId(), "Reference ID cannot be empty");
            Assert.hasText(item.getData(), "Document data cannot be empty");
        });
        log.info("Storing text documents into database. size={}", items.size());

        // Step 1: Calculate MD5 and collection name for all items and group by collection
        Map<String, List<DocumentItemInfo>> itemsByCollection = new HashMap<>();
        Map<String, TextStoreResponseItem> resultMap = new HashMap<>(items.size());
        List<TextStoreResponseItem> resultList = new ArrayList<>(items.size());
        // Process each item to calculate MD5 and determine collection
        for (TextStoreRequestItem item : items) {
            // Create result item
            TextStoreResponseItem result = new TextStoreResponseItem();
            result.setRefId(item.getRefId());
            try {
                // Record text length metric
                meterRegistry.summary(METRIC_TEXT_LENGTH).record(item.getData().length());

                // Calculate MD5 and determine collection name
                String md5 = MD5Util.getMd5String(item.getData());
                String collectionName = HashUtil.getCollectionName(md5);
                result.setId(HashUtil.formatMd5(md5, collectionName));
                // Store in result map for later retrieval
                resultMap.put(md5, result);

                // Group by collection name
                DocumentItemInfo itemInfo = new DocumentItemInfo(item, md5);
                itemsByCollection.computeIfAbsent(collectionName, k -> new ArrayList<>()).add(itemInfo);
                log.debug("Processed item: md5={}, collection={}, refId={}", md5, collectionName, item.getRefId());
            } catch (Exception e) {
                result.setCode(ErrorCode.FAILURE.getCode());
                result.setMessage("Error processing item: " + e.getMessage());
                resultMap.put("error-" + UUID.randomUUID(), result);
                log.error("Error processing document item", e);
            }
            resultList.add(result);
        }

        // Step 2: Process each collection group in parallel using CompletableFuture
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (Map.Entry<String, List<DocumentItemInfo>> entry : itemsByCollection.entrySet()) {
            String collectionName = entry.getKey();
            List<DocumentItemInfo> collectionItems = entry.getValue();

            // 使用CompletableFuture并行处理每个集合
            CompletableFuture<Void> future = CompletableFuture.runAsync(
                    () -> processCollectionItems(collectionName, collectionItems, resultMap),
                    executor
            );

            futures.add(future);
        }

        // 等待所有并行任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        log.info("All collection processing tasks completed");

        // Step 3: Collect all results in original order
        for (TextStoreResponseItem item : resultList) {
            for (Map.Entry<String, TextStoreResponseItem> itemEntry : resultMap.entrySet()) {
                if (item.getId().contains(itemEntry.getKey())) {
                    item.setCode(itemEntry.getValue().getCode());
                    item.setMessage(itemEntry.getValue().getMessage());
                    break;
                }
            }
        }

        return resultList;
    }

    /**
     * 处理一个集合中的所有文档项，使用单一的upsert操作减少MongoDB交互
     *
     * @param collectionName  集合名称
     * @param collectionItems 要处理的文档项列表
     * @param resultMap       结果映射
     */
    private void processCollectionItems(String collectionName, List<DocumentItemInfo> collectionItems,
                                        Map<String, TextStoreResponseItem> resultMap) {
        try {
            long currentTime = System.currentTimeMillis();
            long startTime = System.currentTimeMillis();

            // 计算最佳批处理大小
            int batchSize = calculateOptimalBatchSize(collectionItems);
            log.info("Processing {} items in collection {} with batch size {}",
                    collectionItems.size(), collectionName, batchSize);

            // 批量处理所有文档，使用单一的upsert操作
            batchUpsertDocuments(collectionName, collectionItems, currentTime, resultMap, batchSize);

            // 记录操作时间指标
            long duration = System.currentTimeMillis() - startTime;
            meterRegistry.timer(METRIC_MONGODB_OPERATION,
                            "operation", "upsert",
                            "collection", collectionName)
                    .record(duration, java.util.concurrent.TimeUnit.MILLISECONDS);

            log.info("Completed processing {} items in collection {} in {} ms",
                    collectionItems.size(), collectionName, duration);

        } catch (Exception e) {
            handleCollectionProcessingError(collectionItems, resultMap, e);
        }
    }

    /**
     * 根据文档特性计算最佳批处理大小
     *
     * @param items 要处理的文档项列表
     * @return 最佳批处理大小
     */
    private int calculateOptimalBatchSize(List<DocumentItemInfo> items) {
        // 计算平均文档大小
        int avgDocSize = items.stream()
                .mapToInt(item -> item.item().getData().length())
                .sum() / Math.max(1, items.size());

        // 根据文档大小调整批大小
        if (avgDocSize > 100000) return 10;      // 非常大的文档
        else if (avgDocSize > 10000) return 25;  // 大文档
        else if (avgDocSize > 1000) return 50;   // 中等文档
        else return 100;                         // 小文档
    }

    /**
     * 批量处理文档，使用单一的upsert操作替代分开的查询、插入和更新
     * <p>
     * 主要优化点：
     * 1. 将原来的“查询已存在文档 -> 插入新文档 -> 更新引用”三步操作合并为单一的upsert操作
     * 2. 使用setOnInsert确保只有在文档不存在时才设置初始字段
     * 3. 按批处理，批大小根据文档大小动态调整
     * 4. 更精细的错误处理，单个文档错误不影响整个批处理
     *
     * @param collectionName  集合名称
     * @param collectionItems 要处理的文档项列表
     * @param currentTime     当前时间戳
     * @param resultMap       结果映射
     * @param batchSize       批处理大小
     */
    private void batchUpsertDocuments(String collectionName, List<DocumentItemInfo> collectionItems,
                                      long currentTime, Map<String, TextStoreResponseItem> resultMap, int batchSize) {

        // 按批处理所有文档，每批batchSize个
        // 这样可以避免一次处理过多文档导致的内存压力和数据库负载
        for (int i = 0; i < collectionItems.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, collectionItems.size());
            List<DocumentItemInfo> batch = collectionItems.subList(i, endIndex);

            try {
                // 创建BulkOperations对象，使用UNORDERED模式可以在出错时继续处理其他文档
                BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, collectionName);

                // 对批中的每个文档使用单一的upsert操作
                for (DocumentItemInfo itemInfo : batch) {
                    try {
                        // 构建查询条件，根据MD5查找文档
                        Query query = new Query(Criteria.where("_id").is(itemInfo.md5()));

                        // 构建更新操作，使用setOnInsert确保只有在插入新文档时才设置这些字段
                        // 这是一个关键优化点，避免了对已存在文档的不必要更新
                        Update update = new Update()
                                .setOnInsert("d", CompressionUtil.compressText(itemInfo.item().getData()))
                                .setOnInsert("hit", 0)
                                .setOnInsert("ts", currentTime)
                                .addToSet("ref", itemInfo.item().getRefId())  // 使用addToSet而非普通的set来避免重复引用
                                .set("uts", currentTime);  // 更新时间戳总是更新

                        // 添加到批操作中
                        bulkOps.upsert(query, update);

                        // 记录指标，用于监控和统计
                        meterRegistry.counter(METRIC_STORE, "type", "data").increment();
                    } catch (Exception e) {
                        // 处理单个文档的错误，但继续处理批中的其他文档
                        // 这样可以提高系统的健壮性，单个文档的错误不会影响整个批
                        TextStoreResponseItem result = resultMap.get(itemInfo.md5());
                        if (result != null) {
                            result.setCode(ErrorCode.FAILURE.getCode());
                            result.setMessage("Error preparing document: " + e.getMessage());
                        }
                        log.error("Error preparing document for upsert: md5={}", itemInfo.md5(), e);
                    }
                }

                // 执行批量操作，这里只需要一次MongoDB交互
                BulkWriteResult writeResult = bulkOps.execute();
                log.info("Batch upserted {} documents to collection {}: {} inserted, {} modified",
                        batch.size(), collectionName,
                        writeResult.getInsertedCount(), writeResult.getModifiedCount());

            } catch (BulkOperationException bulkOperationException) {
                // 处理Spring Data MongoDB批量操作异常，特别处理重复键错误
                handleBulkOperationExceptionWithRetry(batch, resultMap, bulkOperationException, collectionName, currentTime);
            } catch (Exception e) {
                // 处理其他类型的异常
                handleGenericBatchError(batch, resultMap, e);
            }
        }
    }

    /**
     * 处理Spring Data MongoDB批量操作异常，并对重复键错误进行重试
     *
     * @param batch                  批处理的文档项
     * @param resultMap              结果映射
     * @param bulkOperationException Spring Data MongoDB批量操作异常
     * @param collectionName         集合名称
     * @param currentTime            当前时间戳
     */
    private void handleBulkOperationExceptionWithRetry(List<DocumentItemInfo> batch, Map<String, TextStoreResponseItem> resultMap,
                                                       BulkOperationException bulkOperationException,
                                                       String collectionName,
                                                       long currentTime) {

        List<DocumentItemInfo> duplicateKeyRetries = new ArrayList<>();
        int otherErrorCount = 0;

        // Spring Data MongoDB的BulkOperationException包含了详细的错误信息
        String errorMessage = bulkOperationException.getMessage();
        boolean hasDuplicateKeyError = errorMessage != null && errorMessage.contains("E11000 duplicate key error");

        int errorCount = bulkOperationException.getErrors().size();
        if (hasDuplicateKeyError) {
            // 对于重复键错误，将失败的文档加入重试列表
            for (int i = 0; i < Math.min(errorCount, batch.size()); i++) {
                duplicateKeyRetries.add(batch.get(bulkOperationException.getErrors().get(i).getIndex()));
            }

            log.warn("Detected {} duplicate key errors in batch operation, will retry with update operation", duplicateKeyRetries.size());
        } else {
            // 其他类型的错误，标记所有失败的文档
            otherErrorCount = errorCount;

            for (int i = 0; i < Math.min(errorCount, batch.size()); i++) {
                DocumentItemInfo itemInfo = batch.get(i);
                TextStoreResponseItem result = resultMap.get(itemInfo.md5());
                if (result != null) {
                    result.setCode(ErrorCode.FAILURE.getCode());
                    result.setMessage("MongoDB error: " + errorMessage);
                }
            }
        }

        // 对重复键错误的文档执行纯更新操作
        if (!duplicateKeyRetries.isEmpty()) {
            retryWithUpdateOperation(duplicateKeyRetries, resultMap, collectionName, currentTime);
        }

        // 记录批量写入统计信息
        log.info("Bulk write completed with {} successful operations, {} duplicate key retries, {} other errors. Inserted: {}, Modified: {}",
                batch.size() - bulkOperationException.getErrors().size(),
                duplicateKeyRetries.size(),
                otherErrorCount,
                bulkOperationException.getResult().getInsertedCount(),
                bulkOperationException.getResult().getModifiedCount());
    }

    /**
     * 对重复键错误的文档执行纯更新操作
     *
     * @param retryItems     需要重试的文档项
     * @param resultMap      结果映射
     * @param collectionName 集合名称
     * @param currentTime    当前时间戳
     */
    private void retryWithUpdateOperation(List<DocumentItemInfo> retryItems,
                                          Map<String, TextStoreResponseItem> resultMap,
                                          String collectionName,
                                          long currentTime) {
        try {
            BulkOperations retryBulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, collectionName);

            for (DocumentItemInfo itemInfo : retryItems) {
                // 构建查询条件
                Query query = new Query(Criteria.where("_id").is(itemInfo.md5()));

                // 构建纯更新操作（不使用upsert，因为文档已存在）
                Update update = new Update()
                        .addToSet("ref", itemInfo.item().getRefId())  // 添加引用到集合中
                        .set("uts", currentTime);  // 更新时间戳

                retryBulkOps.updateMulti(query, update);

                // 记录指标
                meterRegistry.counter(METRIC_STORE, "type", "ref").increment();
            }

            // 执行纯更新操作
            BulkWriteResult retryResult = retryBulkOps.execute();
            log.info("Retry update operation completed for {} documents: {} modified",
                    retryItems.size(), retryResult.getModifiedCount());

            // 标记重试成功的文档
            for (DocumentItemInfo itemInfo : retryItems) {
                TextStoreResponseItem result = resultMap.get(itemInfo.md5());
                if (result != null) {
                    result.setCode(0); // 成功状态码
                    result.setMessage("Successfully added reference to existing document");
                }
            }

        } catch (Exception retryException) {
            // 重试也失败了，标记这些文档为失败
            for (DocumentItemInfo itemInfo : retryItems) {
                TextStoreResponseItem result = resultMap.get(itemInfo.md5());
                if (result != null) {
                    result.setCode(ErrorCode.FAILURE.getCode());
                    result.setMessage("Retry update failed: " + retryException.getMessage());
                }
            }
            log.error("Retry update operation failed for {} documents", retryItems.size(), retryException);
        }
    }

    /**
     * 处理集合处理错误，当整个集合的处理过程出错时调用
     *
     * @param collectionItems 要处理的文档项列表
     * @param resultMap       结果映射
     * @param e               异常
     */
    private void handleCollectionProcessingError(List<DocumentItemInfo> collectionItems,
                                                 Map<String, TextStoreResponseItem> resultMap, Exception e) {
        // 将错误信息设置到该集合中所有文档的结果中
        for (DocumentItemInfo itemInfo : collectionItems) {
            TextStoreResponseItem result = resultMap.get(itemInfo.md5());
            if (result != null) {
                result.setCode(ErrorCode.FAILURE.getCode());
                result.setMessage("Error processing collection: " + e.getMessage());
            }
        }
        // 记录错误日志，包含异常堆栈信息
        log.error("Error processing collection items", e);
    }

    /**
     * 处理一般性批量操作错误（非BulkOperationException）
     *
     * @param batch     批处理的文档项
     * @param resultMap 结果映射
     * @param e         异常
     */
    private void handleGenericBatchError(List<DocumentItemInfo> batch, Map<String, TextStoreResponseItem> resultMap, Exception e) {
        // 处理非BulkOperationException的其他类型异常
        for (DocumentItemInfo itemInfo : batch) {
            TextStoreResponseItem result = resultMap.get(itemInfo.md5());
            if (result != null) {
                result.setCode(ErrorCode.FAILURE.getCode());
                result.setMessage("Error processing document: " + e.getMessage());
            }
        }
        log.error("Error in batch operation for {} documents", batch.size(), e);
    }

    /**
     * Helper class to store document item information during batch processing
     */
    private record DocumentItemInfo(TextStoreRequestItem item, String md5) {
    }

    @Override
    public List<FetchResponseItem> fetchDocuments(List<String> ldocIdList) {
        return fetchDocuments(ldocIdList, false);
    }

    /**
     * Fetch documents by their IDs.
     *
     * @param ldocIdList List of document IDs
     * @return List of fetch results
     */
    public List<FetchResponseItem> fetchDocuments(List<String> ldocIdList, boolean isFetchAllProps) {
        Assert.notEmpty(ldocIdList, "Document ID list cannot be empty");
        ldocIdList.forEach(docId -> Assert.hasText(docId, "Document ID cannot be empty"));

        // 存储最终结果
        List<FetchResponseItem> results = new ArrayList<>(ldocIdList.size());
        // 按照 collectionName 分组的有效文档ID
        Map<String, List<String>> validDocsByCollection = new HashMap<>();
        // 保存 docId 到 md5 的映射，用于后续构建结果
        Map<String, String> docIdToMd5Map = new HashMap<>();

        // 第一步：验证所有文档ID并分组
        for (String docId : ldocIdList) {
            FetchResponseItem result = new FetchResponseItem();
            result.setId(docId);

            // 验证ID格式
            if (!HashUtil.isValidId(docId)) {
                result.setCode(ErrorCode.PARAM_PARSE_ERROR.getCode());
                result.setMessage("Invalid ID format. Expected: ${ldoc_XXX:MD5HASH}");
                log.warn("Invalid document ID format: {}", docId);
                results.add(result);
                continue;
            }

            // 提取MD5和集合名称
            String md5 = HashUtil.extractMd5(docId);
            String collectionName = HashUtil.extractCollectionName(docId);

            // 验证集合名称是否与MD5匹配
            String expectedCollection = HashUtil.getCollectionName(md5);
            if (!expectedCollection.equals(collectionName)) {
                result.setCode(ErrorCode.PARAM_PARSE_ERROR.getCode());
                result.setMessage("Collection name in ID doesn't match expected collection");
                log.warn("Collection name mismatch in document ID: {}, expected: {}", collectionName, expectedCollection);
                results.add(result);
                continue;
            }

            // 将有效的文档ID按集合名称分组
            validDocsByCollection.computeIfAbsent(collectionName, k -> new ArrayList<>()).add(md5);
            docIdToMd5Map.put(docId, md5);
        }

        // 第二步：按集合批量查询文档
        Map<String, DataDocument> fetchedDocs = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : validDocsByCollection.entrySet()) {
            String collectionName = entry.getKey();
            List<String> md5List = entry.getValue();

            // 构建批量查询条件
            Query query = new Query(Criteria.where("_id").in(md5List));
            if (!isFetchAllProps) {
                query.fields().exclude("ref");
            }
            List<DataDocument> docs = mongoTemplate.find(query, DataDocument.class, collectionName);

            // 异步更新所有文档的命中计数
            for (DataDocument doc : docs) {
                String md5 = doc.getId();
                updateDocumentHitCount(md5, collectionName);
                fetchedDocs.put(collectionName + ":" + md5, doc);
            }
        }

        // 第三步：构建最终结果
        for (String docId : ldocIdList) {
            // 跳过已经添加到结果中的无效文档ID
            if (results.stream().anyMatch(item -> item.getId().equals(docId))) {
                continue;
            }

            FetchResponseItem result = new FetchResponseItem();
            result.setId(docId);

            try {
                String md5 = docIdToMd5Map.get(docId);
                String collectionName = HashUtil.extractCollectionName(docId);
                String key = collectionName + ":" + md5;

                DataDocument doc = fetchedDocs.get(key);
                if (doc != null) {
                    // 文档存在，设置结果
                    result.setData(CompressionUtil.decompressToText(doc.getD()));
                    result.setHit(doc.getHit() + 1); // 增加命中计数
                    result.setTs(doc.getTs());
                    result.setUts(doc.getUts());
                    log.info("Document fetch successful: docId={}", result.getId());
                } else {
                    // 文档不存在
                    result.setCode(ErrorCode.DATA_NOT_EXIST.getCode());
                    result.setMessage("Not Found doc=%s".formatted(result.getId()));
                    log.warn("Document not found: docId={}", result.getId());
                }
            } catch (Exception e) {
                result.setCode(ErrorCode.FAILURE.getCode());
                result.setMessage("Error: " + e.getMessage());
                log.error("Error fetching document: docId={}", docId, e);
            }

            results.add(result);
        }

        return results;
    }

    /**
     * Delete documents or references.
     * Deletes the entire document if it's the last reference.
     *
     * @param docs List of delete request items
     * @return List of delete results
     */
    @Override
    public List<DeleteResponseItem> deleteDocuments(List<DeleteRequestItem> docs) {
        Assert.notEmpty(docs, "Delete request items cannot be empty");
        docs.forEach(item -> {
            Assert.hasText(item.getLdocId(), "Document ID cannot be empty");
            Assert.hasText(item.getRefId(), "Reference ID cannot be empty");
        });

        List<DeleteResponseItem> results = new ArrayList<>();
        for (DeleteRequestItem item : docs) {
            results.add(processDeleteDocument(item));
        }
        return results;
    }

    private void updateDocumentHitCount(String md5, String collectionName) {
        // todo 下面这个方法的功能是异步更新mongo记录的命中数，但在面对高并发的场景下，下面的线程池会造成内存泄露，导致频繁GC，下面是使用Eclipse Memory Analyzer分析的原因：
        //  请仔细阅读，根据日志帮我优化代码，并且优化一下其它使用线程池的地方，防止也出现内存泄露，导致频繁GC
        //    at java.util.ArrayList.grow(I)[Ljava/lang/Object; (ArrayList.java:239)
        //  at java.util.ArrayList.grow()[Ljava/lang/Object; (ArrayList.java:244)
        //  at java.util.ArrayList.add(Ljava/lang/Object;[Ljava/lang/Object;I)V (ArrayList.java:483)
        //  at java.util.ArrayList.add(Ljava/lang/Object;)Z (ArrayList.java:496)
        //  at java.util.stream.Collectors$$Lambda+0x00007f57a40751a8.accept(Ljava/lang/Object;Ljava/lang/Object;)V ()
        //  at java.util.stream.ReduceOps$3ReducingSink.accept(Ljava/lang/Object;)V (ReduceOps.java:169)
        //  at java.util.stream.ReferencePipeline$2$1.accept(Ljava/lang/Object;)V (ReferencePipeline.java:179)
        //  at java.util.Spliterators$ArraySpliterator.forEachRemaining(Ljava/util/function/Consumer;)V (Spliterators.java:1024)
        //  at java.util.stream.AbstractPipeline.copyInto(Ljava/util/stream/Sink;Ljava/util/Spliterator;)V (AbstractPipeline.java:509)
        //  at java.util.stream.AbstractPipeline.wrapAndCopyInto(Ljava/util/stream/Sink;Ljava/util/Spliterator;)Ljava/util/stream/Sink; (AbstractPipeline.java:499)
        //  at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(Ljava/util/stream/PipelineHelper;Ljava/util/Spliterator;)Ljava/lang/Object; (ReduceOps.java:921)
        //  at java.util.stream.AbstractPipeline.evaluate(Ljava/util/stream/TerminalOp;)Ljava/lang/Object; (AbstractPipeline.java:234)
        //  at java.util.stream.ReferencePipeline.collect(Ljava/util/stream/Collector;)Ljava/lang/Object; (ReferencePipeline.java:682)
        //  at com.mongodb.internal.connection.BaseCluster.getCompleteServerSelector(Lcom/mongodb/selector/ServerSelector;Lcom/mongodb/internal/connection/OperationContext$ServerDeprioritization;Lcom/mongodb/internal/connection/Cluster$ServersSnapshot;Lcom/mongodb/connection/ClusterSettings;)Lcom/mongodb/selector/ServerSelector; (BaseCluster.java:366)
        //  at com.mongodb.internal.connection.BaseCluster.createCompleteSelectorAndSelectServer(Lcom/mongodb/selector/ServerSelector;Lcom/mongodb/connection/ClusterDescription;Lcom/mongodb/internal/connection/Cluster$ServersSnapshot;Lcom/mongodb/internal/connection/OperationContext$ServerDeprioritization;Lcom/mongodb/connection/ClusterSettings;)Lcom/mongodb/internal/connection/ServerTuple; (BaseCluster.java:343)
        //  at com.mongodb.internal.connection.BaseCluster.createCompleteSelectorAndSelectServer(Lcom/mongodb/selector/ServerSelector;Lcom/mongodb/connection/ClusterDescription;Lcom/mongodb/internal/connection/OperationContext$ServerDeprioritization;Lcom/mongodb/internal/time/Timeout;Lcom/mongodb/internal/TimeoutContext;)Lcom/mongodb/internal/connection/ServerTuple; (BaseCluster.java:327)
        //  at com.mongodb.internal.connection.BaseCluster.selectServer(Lcom/mongodb/selector/ServerSelector;Lcom/mongodb/internal/connection/OperationContext;)Lcom/mongodb/internal/connection/ServerTuple; (BaseCluster.java:135)
        //  at com.mongodb.internal.connection.AbstractMultiServerCluster.selectServer(Lcom/mongodb/selector/ServerSelector;Lcom/mongodb/internal/connection/OperationContext;)Lcom/mongodb/internal/connection/ServerTuple; (AbstractMultiServerCluster.java:59)
        //  at com.mongodb.internal.binding.ClusterBinding.getWriteConnectionSource()Lcom/mongodb/internal/binding/ConnectionSource; (ClusterBinding.java:100)
        //  at com.mongodb.client.internal.ClientSessionBinding$$Lambda+0x00007f57a4bea1e0.get()Ljava/lang/Object; ()
        //  at com.mongodb.client.internal.ClientSessionBinding.getConnectionSource(Ljava/util/function/Supplier;)Lcom/mongodb/internal/binding/ConnectionSource; (ClientSessionBinding.java:108)
        //  at com.mongodb.client.internal.ClientSessionBinding.getWriteConnectionSource()Lcom/mongodb/internal/binding/ConnectionSource; (ClientSessionBinding.java:98)
        //  at com.mongodb.internal.operation.MixedBulkWriteOperation$$Lambda+0x00007f57a4be8ed8.get()Ljava/lang/Object; ()
        //  at com.mongodb.internal.operation.SyncOperationHelper.withSuppliedResource(Ljava/util/function/Supplier;ZLjava/util/function/Function;)Ljava/lang/Object; (SyncOperationHelper.java:148)
        //  at com.mongodb.internal.operation.SyncOperationHelper.withSourceAndConnection(Ljava/util/function/Supplier;ZLjava/util/function/BiFunction;)Ljava/lang/Object; (SyncOperationHelper.java:129)
        //  at com.mongodb.internal.operation.MixedBulkWriteOperation.lambda$execute$3(Lcom/mongodb/internal/binding/WriteBinding;Lcom/mongodb/internal/async/function/RetryState;Lcom/mongodb/internal/TimeoutContext;)Lcom/mongodb/bulk/BulkWriteResult; (MixedBulkWriteOperation.java:190)
        //  at com.mongodb.internal.operation.MixedBulkWriteOperation$$Lambda+0x00007f57a4be4878.get()Ljava/lang/Object; ()
        //  at com.mongodb.internal.operation.MixedBulkWriteOperation.lambda$decorateWriteWithRetries$0(Lcom/mongodb/internal/async/function/RetryState;Lcom/mongodb/internal/connection/OperationContext;Ljava/util/function/Supplier;)Ljava/lang/Object; (MixedBulkWriteOperation.java:147)
        //  at com.mongodb.internal.operation.MixedBulkWriteOperation$$Lambda+0x00007f57a4be38f0.get()Ljava/lang/Object; ()
        //  at com.mongodb.internal.async.function.RetryingSyncSupplier.get()Ljava/lang/Object; (RetryingSyncSupplier.java:67)
        //  at com.mongodb.internal.operation.MixedBulkWriteOperation.execute(Lcom/mongodb/internal/binding/WriteBinding;)Lcom/mongodb/bulk/BulkWriteResult; (MixedBulkWriteOperation.java:209)
        //  at com.mongodb.internal.operation.MixedBulkWriteOperation.execute(Lcom/mongodb/internal/binding/WriteBinding;)Ljava/lang/Object; (MixedBulkWriteOperation.java:79)
        //  at com.mongodb.client.internal.MongoClusterImpl$OperationExecutorImpl.execute(Lcom/mongodb/internal/operation/WriteOperation;Lcom/mongodb/ReadConcern;Lcom/mongodb/client/ClientSession;)Ljava/lang/Object; (MongoClusterImpl.java:379)
        //  at com.mongodb.client.internal.MongoCollectionImpl.executeSingleWriteRequest(Lcom/mongodb/client/ClientSession;Lcom/mongodb/internal/operation/WriteOperation;Lcom/mongodb/internal/bulk/WriteRequest$Type;)Lcom/mongodb/bulk/BulkWriteResult; (MongoCollectionImpl.java:1116)
        //  at com.mongodb.client.internal.MongoCollectionImpl.executeUpdate(Lcom/mongodb/client/ClientSession;Lorg/bson/conversions/Bson;Lorg/bson/conversions/Bson;Lcom/mongodb/client/model/UpdateOptions;Z)Lcom/mongodb/client/result/UpdateResult; (MongoCollectionImpl.java:1099)
        //  at com.mongodb.client.internal.MongoCollectionImpl.updateOne(Lorg/bson/conversions/Bson;Lorg/bson/conversions/Bson;Lcom/mongodb/client/model/UpdateOptions;)Lcom/mongodb/client/result/UpdateResult; (MongoCollectionImpl.java:608)
        //  at org.springframework.data.mongodb.core.MongoTemplate.lambda$doUpdate$21(Lorg/bson/Document;Lorg/bson/Document;Ljava/lang/String;Lcom/mongodb/WriteConcern;Lorg/springframework/data/mongodb/core/QueryOperations$UpdateContext;Lorg/springframework/data/mongodb/core/mapping/MongoPersistentEntity;Ljava/lang/Class;ZLcom/mongodb/client/model/UpdateOptions;Lcom/mongodb/client/MongoCollection;)Lcom/mongodb/client/result/UpdateResult; (MongoTemplate.java:1755)
        //  at org.springframework.data.mongodb.core.MongoTemplate$$Lambda+0x00007f57a4bddb48.doInCollection(Lcom/mongodb/client/MongoCollection;)Ljava/lang/Object; ()
        //  at org.springframework.data.mongodb.core.MongoTemplate.execute(Ljava/lang/String;Lorg/springframework/data/mongodb/core/CollectionCallback;)Ljava/lang/Object; (MongoTemplate.java:603)
        //  at org.springframework.data.mongodb.core.MongoTemplate.doUpdate(Ljava/lang/String;Lorg/springframework/data/mongodb/core/query/Query;Lorg/springframework/data/mongodb/core/query/UpdateDefinition;Ljava/lang/Class;ZZ)Lcom/mongodb/client/result/UpdateResult; (MongoTemplate.java:1728)
        //  at org.springframework.data.mongodb.core.MongoTemplate.updateFirst(Lorg/springframework/data/mongodb/core/query/Query;Lorg/springframework/data/mongodb/core/query/UpdateDefinition;Ljava/lang/String;)Lcom/mongodb/client/result/UpdateResult; (MongoTemplate.java:1651)
        //  at com.iflytek.lynxiao.datashard.service.impl.DatashardServiceImpl.lambda$updateDocumentHitCount$8(Ljava/lang/String;Ljava/lang/String;)V (DatashardServiceImpl.java:611)
        //  at com.iflytek.lynxiao.datashard.service.impl.DatashardServiceImpl$$Lambda+0x00007f57a4bd4ef0.run()V ()
        //  at java.util.concurrent.CompletableFuture$AsyncRun.run()V (CompletableFuture.java:1804)
        //  at java.util.concurrent.ThreadPoolExecutor.runWorker(Ljava/util/concurrent/ThreadPoolExecutor$Worker;)V (ThreadPoolExecutor.java:1144)
        //  at java.util.concurrent.ThreadPoolExecutor$Worker.run()V (ThreadPoolExecutor.java:642)
        //  at java.lang.Thread.runWith(Ljava/lang/Object;Ljava/lang/Runnable;)V (Thread.java:1596)
        //  at java.lang.Thread.run()V (Thread.java:1583)

        CompletableFuture.runAsync(() -> {
            try {
                Query query = new Query(Criteria.where("_id").is(md5));
                mongoTemplate.updateFirst(query, UPDATE, collectionName);
                log.debug("Updated document hit count async: md5={}, collection={}", md5, collectionName);
            } catch (Exception e) {
                log.error("Error updating hit count async: md5={}", md5, e);
            }
        }, asyncExecutor);
    }

    private DeleteResponseItem processDeleteDocument(DeleteRequestItem item) {
        DeleteResponseItem result = new DeleteResponseItem();
        result.setLdocId(item.getLdocId());

        try {
            // Validate ID format
            if (!isValidDeleteId(item.getLdocId(), result)) {
                return result;
            }
            // Extract MD5 and collection name
            String md5 = HashUtil.extractMd5(item.getLdocId());
            String collectionName = HashUtil.extractCollectionName(item.getLdocId());

            // Validate collection name matches MD5
            if (!isValidDeleteCollectionForMd5(md5, collectionName, result)) {
                return result;
            }

            // Find and process document deletion
            Optional<DataDocument> docOpt = Optional.ofNullable(mongoTemplate.findById(md5, DataDocument.class, collectionName));

            if (docOpt.isPresent()) {
                DataDocument doc = docOpt.get();
                if (doc.getRef().contains(item.getRefId())) {
                    doc.getRef().remove(item.getRefId());
                    // Update reference
                    if (doc.getRef().isEmpty()) {
                        mongoTemplate.remove(doc, collectionName);
                        result.setRefCount(0);
                        log.info("Deleted entire document: md5={}", doc.getId());
                    } else {
                        Query query = new Query(Criteria.where("_id").is(md5));
                        Update update = new Update().pull("ref", item.getRefId()).set("uts", System.currentTimeMillis());
                        mongoTemplate.updateFirst(query, update, collectionName);
                        result.setRefCount(doc.getRef().size());
                        log.info("Removed reference from document: md5={}, ref={}, remaining refs: {}",
                                md5, item.getRefId(), doc.getRef().size());
                    }
                } else {
                    result.setCode(ErrorCode.REF_NOT_EXIST.getCode());
                    result.setMessage("Reference not found");
                    log.warn("Reference to delete not found: md5={}, ref={}", md5, item.getRefId());
                }
            } else {
                result.setCode(ErrorCode.DATA_NOT_EXIST.getCode());
                result.setMessage("Document not found");
                log.warn("Document to delete not found: md5={}", result.getLdocId());
            }

        } catch (Exception e) {
            result.setCode(ErrorCode.FAILURE.getCode());
            result.setMessage("Error: " + e.getMessage());
            log.error("Error deleting document: md5={}", item.getLdocId(), e);
        }

        return result;
    }

    private boolean isValidDeleteId(String ldocId, DeleteResponseItem result) {
        if (!HashUtil.isValidId(ldocId)) {
            result.setCode(ErrorCode.PARAM_VALID_ERROR.getCode());
            result.setMessage("Invalid ID format. Expected: ${ldoc_XXX:MD5HASH}");
            log.warn("Invalid document ID format for deletion: {}", ldocId);
            return false;
        }
        return true;
    }

    private boolean isValidDeleteCollectionForMd5(String md5, String collectionName, DeleteResponseItem result) {
        String expectedCollection = HashUtil.getCollectionName(md5);
        if (!expectedCollection.equals(collectionName)) {
            result.setCode(ErrorCode.PARAM_VALID_ERROR.getCode());
            result.setMessage("Collection name mismatch");
            log.warn("Collection name mismatch in delete operation: {}, expected: {}",
                    collectionName, expectedCollection);
            return false;
        }
        return true;
    }
}
